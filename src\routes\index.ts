import { Router, Request, Response } from 'express';
import { whatsAppRoutes } from './whatsapp';
import aiRoutes from './ai';
import dataRoutes from './data';
import testRoutes from './test';

/**
 * Main routes configuration
 */
export class Routes {
  public router: Router;

  constructor() {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Health check endpoint
    this.router.get('/health', (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env['NODE_ENV'] || 'development'
      });
    });

    // API info endpoint
    this.router.get('/', (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: 'Welcome to Cravin Concierge API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          whatsapp: '/api/whatsapp/webhook',
          ai: '/api/ai/chat',
          data: '/api/data/stats',
          test: '/api/test/all'
        }
      });
    });

    // WhatsApp routes
    this.router.use('/whatsapp', whatsAppRoutes);

    // AI routes
    this.router.use('/ai', aiRoutes);

    // Data routes
    this.router.use('/data', dataRoutes);

    // Test routes
    this.router.use('/test', testRoutes);
  }
}

// Export router instance
export const routes = new Routes().router;

import { Router } from 'express';
import { WhatsAppController } from '../controllers/WhatsAppController';

/**
 * WhatsApp routes configuration
 */
export class WhatsAppRoutes {
  public router: Router;
  private whatsAppController: WhatsAppController;

  constructor() {
    this.router = Router();
    this.whatsAppController = new WhatsAppController();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // GET /api/whatsapp/webhook - Webhook verification
    this.router.get('/webhook', this.whatsAppController.verifyWebhook);

    // POST /api/whatsapp/webhook - Handle incoming messages
    this.router.post('/webhook', this.whatsAppController.handleWebhook);

    // GET /api/whatsapp/history/:phoneNumber - Get conversation history
    this.router.get('/history/:phoneNumber', this.whatsAppController.getConversationHistory);

    // DELETE /api/whatsapp/history/:phoneNumber - Clear conversation history
    this.router.delete('/history/:phoneNumber', this.whatsAppController.clearConversationHistory);
  }
}

// Export router instance
export const whatsAppRoutes = new WhatsAppRoutes().router;

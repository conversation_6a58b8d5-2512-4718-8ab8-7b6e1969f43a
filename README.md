# Cravin Concierge - WhatsApp AI Chatbot

A WhatsApp-based conversational AI chatbot built with TypeScript, Express.js, and AWS Bedrock that provides intelligent responses about restaurants, menu items, and business data using vector search capabilities.

## Features

- **WhatsApp Integration**: Seamless webhook integration for receiving and responding to WhatsApp messages
- **Conversational AI**: Powered by AWS Bedrock (Claude 3 Sonnet) for natural language understanding and generation
- **Vector Search**: Intelligent semantic search through business data using similarity matching
- **Real-time Data Loading**: Dynamic loading of vectorized business data from JSON files
- **Multi-type Search**: Support for restaurants, menu items, add-ons, branches, categories, and more
- **Conversation Memory**: Maintains conversation history for contextual responses
- **RESTful API**: Complete API endpoints for testing and integration
- **TypeScript**: Full type safety and modern development experience

## Architecture

```
src/
├── controllers/          # Request handlers
│   ├── AIController.ts      # AI chat and search endpoints
│   ├── WhatsAppController.ts # WhatsApp webhook handling
│   ├── DataController.ts    # Data management endpoints
│   └── TestController.ts    # Testing and validation endpoints
├── services/            # Business logic
│   ├── aiService.ts         # AWS Bedrock integration
│   └── vectorSearchService.ts # Vector search and similarity matching
├── utils/               # Utilities
│   └── dataLoader.ts        # JSON data loading and caching
├── routes/              # API routes
├── types/               # TypeScript type definitions
└── config/              # Configuration management
```

## Prerequisites

- Node.js 18+ and npm
- AWS Account with Bedrock access
- WhatsApp Business API access
- Vectorized data file (`vectorized_data.json`)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Cravin-Concierge
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Configure the following environment variables in `.env`:
   
   ```env
   # Server Configuration
   PORT=3000
   NODE_ENV=development
   
   # WhatsApp Configuration
   WHATSAPP_VERIFY_TOKEN=your_whatsapp_verify_token_here
   WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
   WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
   
   # AWS Configuration
   AWS_REGION=us-east-1
   AWS_ACCESS_KEY_ID=your_aws_access_key_here
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
   
   # AWS Bedrock Configuration
   AWS_BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0
   AWS_BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
   
   # Data Configuration
   VECTORIZED_DATA_PATH=./vectorized_data.json
   ```

4. **Prepare Data File**
   
   Ensure your `vectorized_data.json` file is in the root directory with the following structure:
   ```json
   {
     "timestamp": "2024-01-01T00:00:00Z",
     "total_records": 1000,
     "data": [
       {
         "id": "unique-id",
         "content": "Descriptive text content",
         "metadata": {
           "type": "item|restaurant|addon|branch|category|shop|sports_landing",
           "id": "original-id",
           "name": "Item Name",
           "price": "25.00",
           "branch_name": "Branch Location",
           "restaurant_name": "Restaurant Name"
         }
       }
     ]
   }
   ```

## Usage

### Development

```bash
# Start development server with hot reload
npm run dev
```

### Production

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Testing

```bash
# Run all tests
npm test

# Test specific functionality via API
curl http://localhost:3000/api/test/all
```

## API Endpoints

### Core Endpoints

- `GET /api/health` - Health check
- `GET /api/` - API information and available endpoints

### WhatsApp Integration

- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Message handling
- `GET /api/whatsapp/history/:phoneNumber` - Get conversation history
- `DELETE /api/whatsapp/history/:phoneNumber` - Clear conversation history

### AI Chat & Search

- `POST /api/ai/chat` - Conversational AI endpoint
- `POST /api/ai/search` - Search business data
- `GET /api/ai/history/:sessionId` - Get chat history
- `POST /api/ai/suggestions` - Get input suggestions
- `GET /api/ai/health` - AI service health check

### Data Management

- `GET /api/data/stats` - Data statistics
- `GET /api/data/type/:type` - Get data by type
- `GET /api/data/item/:id` - Get specific data item
- `POST /api/data/search` - Search data
- `POST /api/data/reload` - Reload data from file
- `GET /api/data/random` - Get random data items

### Testing & Validation

- `GET /api/test/all` - Run all system tests
- `GET /api/test/data-loading` - Test data loading
- `POST /api/test/vector-search` - Test search functionality
- `POST /api/test/ai-service` - Test AI service
- `POST /api/test/chatbot-flow` - Test complete chatbot flow
- `GET /api/test/config` - Test configuration

## WhatsApp Setup

1. **Create WhatsApp Business Account**
   - Set up WhatsApp Business API
   - Get access token and phone number ID

2. **Configure Webhook**
   - Set webhook URL to: `https://your-domain.com/api/whatsapp/webhook`
   - Use your verify token from environment variables
   - Subscribe to message events

3. **Test Integration**
   - Send a message to your WhatsApp Business number
   - Check logs for webhook reception and AI response

## AWS Bedrock Setup

1. **Enable Bedrock Models**
   - Enable Claude 3 Sonnet model access
   - Enable Titan Embedding model access

2. **Configure IAM**
   - Create IAM user with Bedrock permissions
   - Add access keys to environment variables

3. **Test Connection**
   ```bash
   curl -X GET http://localhost:3000/api/ai/health
   ```

## Data Management

The system loads business data from a JSON file containing vectorized information about:

- **Restaurants**: Business information and locations
- **Menu Items**: Food items with descriptions and prices
- **Add-ons**: Additional items and modifications
- **Branches**: Location-specific information
- **Categories**: Item categorization
- **Shops**: Store information
- **Sports Landing**: Sports-related content

### Data Updates

To update the data:

1. Replace the `vectorized_data.json` file
2. Call the reload endpoint: `POST /api/data/reload`
3. The system will automatically detect and load new data

## Monitoring & Debugging

### Logs

The application provides detailed logging for:
- WhatsApp message processing
- AI service interactions
- Vector search operations
- Data loading activities

### Health Checks

Monitor system health using:
- `GET /api/health` - Overall system health
- `GET /api/ai/health` - AI service health
- `GET /api/test/all` - Comprehensive system test

### Performance Testing

Use the test endpoints to validate:
- Response times
- Search accuracy
- AI response quality
- Data loading performance

## Troubleshooting

### Common Issues

1. **WhatsApp webhook not receiving messages**
   - Verify webhook URL is accessible
   - Check verify token configuration
   - Ensure HTTPS is enabled in production

2. **AI responses not working**
   - Verify AWS credentials
   - Check Bedrock model access
   - Test with `/api/ai/health` endpoint

3. **Data not loading**
   - Verify JSON file path and format
   - Check file permissions
   - Use `/api/test/data-loading` to diagnose

4. **Search returning no results**
   - Check similarity threshold settings
   - Verify data content format
   - Test with `/api/test/vector-search`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

import fs from 'fs';
import path from 'path';
import { VectorizedDataFile, VectorizedDataItem } from '../types/common';
import { config } from '../config/environment';

/**
 * Data Loader Utility
 * Handles loading and caching of vectorized data from JSON file
 */
export class DataLoader {
  private static instance: DataLoader;
  private data: VectorizedDataItem[] = [];
  private lastLoadTime: Date | null = null;
  private readonly dataPath: string;

  private constructor() {
    this.dataPath = path.resolve(config.data.vectorizedDataPath);
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): DataLoader {
    if (!DataLoader.instance) {
      DataLoader.instance = new DataLoader();
    }
    return DataLoader.instance;
  }

  /**
   * Load data from JSON file
   */
  public async loadData(): Promise<VectorizedDataItem[]> {
    try {
      // Check if file exists
      if (!fs.existsSync(this.dataPath)) {
        throw new Error(`Vectorized data file not found at: ${this.dataPath}`);
      }

      // Get file stats to check if reload is needed
      const stats = fs.statSync(this.dataPath);
      const fileModTime = stats.mtime;

      // If data is already loaded and file hasn't changed, return cached data
      if (this.data.length > 0 && this.lastLoadTime && fileModTime <= this.lastLoadTime) {
        console.log('Using cached vectorized data');
        return this.data;
      }

      console.log('Loading vectorized data from file...');
      
      // Read and parse the JSON file
      const fileContent = fs.readFileSync(this.dataPath, 'utf-8');
      const parsedData: VectorizedDataFile = JSON.parse(fileContent);

      // Validate data structure
      if (!parsedData.data || !Array.isArray(parsedData.data)) {
        throw new Error('Invalid data structure in vectorized data file');
      }

      // Cache the data
      this.data = parsedData.data;
      this.lastLoadTime = new Date();

      console.log(`Loaded ${this.data.length} vectorized data items`);
      return this.data;
    } catch (error) {
      console.error('Error loading vectorized data:', error);
      throw error;
    }
  }

  /**
   * Get all data items
   */
  public async getData(): Promise<VectorizedDataItem[]> {
    if (this.data.length === 0) {
      await this.loadData();
    }
    return this.data;
  }

  /**
   * Get data items by type
   */
  public async getDataByType(type: VectorizedDataItem['metadata']['type']): Promise<VectorizedDataItem[]> {
    const data = await this.getData();
    return data.filter(item => item.metadata.type === type);
  }

  /**
   * Get data item by ID
   */
  public async getDataById(id: string): Promise<VectorizedDataItem | null> {
    const data = await this.getData();
    return data.find(item => item.id === id) || null;
  }

  /**
   * Search data items by content (simple text search)
   */
  public async searchByContent(query: string): Promise<VectorizedDataItem[]> {
    const data = await this.getData();
    const lowerQuery = query.toLowerCase();
    
    return data.filter(item => 
      item.content.toLowerCase().includes(lowerQuery) ||
      JSON.stringify(item.metadata).toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Get data statistics
   */
  public async getDataStats(): Promise<{
    total: number;
    byType: Record<string, number>;
    lastLoaded: Date | null;
  }> {
    const data = await this.getData();
    
    const byType: Record<string, number> = {};
    data.forEach(item => {
      byType[item.metadata.type] = (byType[item.metadata.type] || 0) + 1;
    });

    return {
      total: data.length,
      byType,
      lastLoaded: this.lastLoadTime
    };
  }

  /**
   * Force reload data from file
   */
  public async reloadData(): Promise<VectorizedDataItem[]> {
    this.data = [];
    this.lastLoadTime = null;
    return await this.loadData();
  }

  /**
   * Get random data items (for testing/examples)
   */
  public async getRandomItems(count: number = 5): Promise<VectorizedDataItem[]> {
    const data = await this.getData();
    const shuffled = [...data].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
}

// Export singleton instance
export const dataLoader = DataLoader.getInstance();
